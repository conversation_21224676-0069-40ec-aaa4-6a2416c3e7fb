{"rustc": 383397013764560953, "features": "[\"bincode\", \"serde\"]", "declared_features": "[\"bincode\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 3360161622638113394, "profile": 6652793396284126538, "path": 17535632690796572909, "deps": [[9689903380558560274, "serde", false, 8537716410839897940], [10570260326288551891, "solana_instruction", false, 6543215718921772342], [10889494155287625682, "serde_bytes", false, 8763507276661656898], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [14591356476411885690, "solana_sdk_ids", false, 1263336053477103343], [15341883195918613377, "solana_system_interface", false, 7026953493720285368], [16257276029081467297, "serde_derive", false, 110293047266484015]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-loader-v3-interface-a527a363108b61b8/dep-lib-solana_loader_v3_interface", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}