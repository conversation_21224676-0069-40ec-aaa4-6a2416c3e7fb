{"rustc": 10895048813736897673, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 2241668132362809309, "path": 3137287978104977033, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bs58-a21d1cee457795bf\\dep-lib-bs58", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}