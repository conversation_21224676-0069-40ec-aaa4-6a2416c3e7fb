{"rustc": 383397013764560953, "features": "[\"associated_token\", \"default\", \"mint\", \"spl-associated-token-account\", \"spl-pod\", \"spl-token\", \"spl-token-2022\", \"spl-token-group-interface\", \"spl-token-metadata-interface\", \"token\", \"token_2022\", \"token_2022_extensions\"]", "declared_features": "[\"anchor-debug\", \"associated_token\", \"borsh\", \"default\", \"devnet\", \"governance\", \"idl-build\", \"memo\", \"metadata\", \"mint\", \"mpl-token-metadata\", \"spl-associated-token-account\", \"spl-memo\", \"spl-pod\", \"spl-token\", \"spl-token-2022\", \"spl-token-group-interface\", \"spl-token-metadata-interface\", \"stake\", \"token\", \"token_2022\", \"token_2022_extensions\"]", "target": 9008755946677022642, "profile": 15657897354478470176, "path": 9853872510841080426, "deps": [[790673365560624081, "spl_pod", false, 17600426445546719696], [9413657500826327667, "spl_token_2022", false, 14309187650303222965], [10497244395353946307, "spl_associated_token_account", false, 17008789226668495869], [12682673687743740477, "spl_token", false, 18148318013240414305], [16226330934343841514, "anchor_lang", false, 10496105585443977654], [17340930586486050809, "spl_token_group_interface", false, 9879314859717878116], [17667569856882013889, "spl_token_metadata_interface", false, 6701156436304463209]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-spl-0e69330cbf977e01/dep-lib-anchor_spl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}