{"rustc": 383397013764560953, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 15019087522015688764, "profile": 2225463790103693989, "path": 18082317389069676551, "deps": [[3060637413840920116, "proc_macro2", false, 10661881203608513514], [4974441333307933176, "syn", false, 10199271570017896114], [15203748914246919255, "proc_macro_crate", false, 12272308972412088756], [17990358020177143287, "quote", false, 12379506894038705714]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/num_enum_derive-96678020390b3c3f/dep-lib-num_enum_derive", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}