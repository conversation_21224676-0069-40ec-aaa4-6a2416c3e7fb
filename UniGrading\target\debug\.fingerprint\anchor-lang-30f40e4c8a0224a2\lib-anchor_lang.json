{"rustc": 383397013764560953, "features": "[\"anchor-lang-idl\", \"derive\", \"idl-build\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-lang-idl\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\", \"interface-instructions\", \"lazy-account\"]", "target": 14695202496702424983, "profile": 15657897354478470176, "path": 13682254467262055188, "deps": [[*****************, "bincode", false, 12460930568304774582], [1205890147656972920, "anchor_derive_accounts", false, 10180162066580947650], [2611905835808443941, "borsh", false, 8197664693353982157], [5532297295134723458, "anchor_attribute_program", false, 14809487206622518147], [6080285880102702883, "anchor_derive_space", false, 11264443326970425279], [6511429716036861196, "bytemuck", false, 9004261617411574230], [8008191657135824715, "thiserror", false, 16276810871065626392], [10220848352499156513, "anchor_lang_idl", false, 3894472442918668057], [10784666044722074209, "anchor_attribute_event", false, 10031056995175342268], [11737296378741312020, "anchor_attribute_account", false, 10840488560831606200], [13139933692030873282, "anchor_attribute_error", false, 5172967294007728327], [13977390777787220484, "anchor_derive_serde", false, 3324930040618900456], [14555048766774983064, "anchor_attribute_constant", false, 5521620655544547394], [16016078550530309219, "solana_program", false, 3212434464576765496], [16070395273854428984, "anchor_attribute_access_control", false, 4209155387344386969], [18066890886671768183, "base64", false, 1917548062779470964]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-30f40e4c8a0224a2/dep-lib-anchor_lang", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}