import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, Dict
from config import config
from database import DeadmanDatabase

class AlertService:
    def __init__(self):
        self.db = DeadmanDatabase()
    
    def send_emergency_alert(self, phone_number: str, timeout_hours: int, 
                           gps_location: Optional[Dict] = None):
        """
        Gửi cảnh b<PERSON>o khẩn cấp qua SMS và email
        """
        message = self._format_emergency_message(timeout_hours, gps_location)
        
        # Thử gửi SMS trước
        sms_success = self._send_sms(phone_number, message)
        
        # Nếu SMS thất bại hoặc không có cấu hình, gửi email
        email_success = False
        if not sms_success:
            email_success = self._send_email(phone_number, "🚨 CẢNH BÁO KHẨN CẤP", message)
        
        return sms_success or email_success
    
    def _format_emergency_message(self, timeout_hours: int, 
                                 gps_location: Optional[Dict] = None) -> str:
        """Format tin nhắn cảnh b<PERSON>o khẩn cấp"""
        if gps_location:
            lat = gps_location['latitude']
            lon = gps_location['longitude']
            gps_time = gps_location.get('timestamp', 'Không rõ')
            maps_url = f"https://maps.google.com/?q={lat},{lon}"
            
            return config.EMERGENCY_MESSAGE_TEMPLATE.format(
                timeout_hours=timeout_hours,
                latitude=lat,
                longitude=lon,
                gps_time=gps_time,
                maps_url=maps_url
            )
        else:
            return f"""
🚨 CẢNH BÁO KHẨN CẤP - DEADMAN SWITCH ACTIVATED 🚨

Người dùng đã không phản hồi trong {timeout_hours} giờ.
Không có thông tin GPS khả dụng.

Vui lòng kiểm tra an toàn ngay lập tức!
"""
    
    def _send_sms(self, phone_number: str, message: str) -> bool:
        """
        Gửi SMS qua Twilio (free tier: $15 credit)
        Hoặc có thể dùng các service khác như TextBelt
        """
        
        # Phương pháp 1: Twilio (cần đăng ký)
        if (config.TWILIO_ACCOUNT_SID and 
            config.TWILIO_AUTH_TOKEN and 
            config.TWILIO_PHONE_NUMBER):
            
            try:
                from twilio.rest import Client
                
                client = Client(config.TWILIO_ACCOUNT_SID, config.TWILIO_AUTH_TOKEN)
                
                message_obj = client.messages.create(
                    body=message,
                    from_=config.TWILIO_PHONE_NUMBER,
                    to=phone_number
                )
                
                print(f"✅ SMS sent via Twilio: {message_obj.sid}")
                return True
                
            except Exception as e:
                print(f"❌ Twilio SMS failed: {e}")
        
        # Phương pháp 2: TextBelt (miễn phí 1 SMS/day)
        try:
            response = requests.post('https://textbelt.com/text', {
                'phone': phone_number,
                'message': message[:160],  # Giới hạn 160 ký tự
                'key': 'textbelt'  # Free tier key
            }, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ SMS sent via TextBelt")
                    return True
                else:
                    print(f"❌ TextBelt SMS failed: {result.get('error')}")
            
        except Exception as e:
            print(f"❌ TextBelt SMS error: {e}")
        
        # Phương pháp 3: SMS Gateway qua email (carrier-specific)
        return self._send_sms_via_email(phone_number, message)
    
    def _send_sms_via_email(self, phone_number: str, message: str) -> bool:
        """
        Gửi SMS qua email gateway của nhà mạng
        Ví dụ: <EMAIL>
        """
        
        # SMS gateways của các nhà mạng Việt Nam (có thể không hoạt động)
        sms_gateways = {
            'viettel': '@sms.viettel.vn',
            'mobifone': '@sms.mobifone.vn', 
            'vinaphone': '@sms.vinaphone.vn'
        }
        
        # Thử tất cả các gateway
        for carrier, gateway in sms_gateways.items():
            try:
                sms_email = phone_number.replace('+84', '0') + gateway
                success = self._send_email(sms_email, "Emergency Alert", message[:160])
                if success:
                    print(f"✅ SMS sent via {carrier} email gateway")
                    return True
            except Exception as e:
                print(f"❌ {carrier} SMS gateway failed: {e}")
        
        return False
    
    def _send_email(self, recipient: str, subject: str, message: str) -> bool:
        """Gửi email cảnh báo"""
        
        if not config.EMAIL_ADDRESS or not config.EMAIL_PASSWORD:
            print("❌ Email not configured")
            return False
        
        try:
            # Tạo email
            msg = MIMEMultipart()
            msg['From'] = config.EMAIL_ADDRESS
            msg['To'] = recipient
            msg['Subject'] = subject
            
            msg.attach(MIMEText(message, 'plain', 'utf-8'))
            
            # Gửi email
            server = smtplib.SMTP(config.SMTP_SERVER, config.SMTP_PORT)
            server.starttls()
            server.login(config.EMAIL_ADDRESS, config.EMAIL_PASSWORD)
            
            text = msg.as_string()
            server.sendmail(config.EMAIL_ADDRESS, recipient, text)
            server.quit()
            
            print(f"✅ Email sent to {recipient}")
            return True
            
        except Exception as e:
            print(f"❌ Email failed: {e}")
            return False
    
    def send_test_alert(self, phone_number: str) -> bool:
        """Gửi tin nhắn test để kiểm tra hệ thống"""
        test_message = """
🧪 TEST - Deadman Switch Travel Safety

Đây là tin nhắn test hệ thống cảnh báo khẩn cấp.
Nếu bạn nhận được tin nhắn này, hệ thống hoạt động bình thường.

Thời gian test: {current_time}
""".format(current_time=str(__import__('datetime').datetime.now()))
        
        return self._send_sms(phone_number, test_message)
    
    def send_ping_reminder(self, phone_number: str, hours_remaining: int):
        """Gửi nhắc nhở ping trước khi hết hạn"""
        reminder_message = f"""
⏰ NHẮC NHỞ PING - Deadman Switch

Bạn cần ping trong vòng {hours_remaining} giờ nữa để tránh kích hoạt cảnh báo khẩn cấp.

Truy cập: http://localhost:5000 để ping ngay.
"""
        
        return self._send_sms(phone_number, reminder_message)
