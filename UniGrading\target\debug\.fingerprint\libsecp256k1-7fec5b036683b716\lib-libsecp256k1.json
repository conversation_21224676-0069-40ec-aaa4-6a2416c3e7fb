{"rustc": 383397013764560953, "features": "[\"sha2\", \"static-context\", \"std\"]", "declared_features": "[\"default\", \"hmac\", \"hmac-drbg\", \"lazy-static-context\", \"lazy_static\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "target": 3229137391415082075, "profile": 15657897354478470176, "path": 16897989142531651485, "deps": [[4731167174326621189, "rand", false, 5369461842099141039], [6374421995994392543, "digest", false, 7702415988173823732], [9529943735784919782, "arrayref", false, 3101610639674147938], [9689903380558560274, "serde", false, 8537716410839897940], [10697153736615144157, "build_script_build", false, 15849924920987587748], [11472355562936271783, "sha2", false, 17914537452805921766], [13443824959912985638, "libsecp256k1_core", false, 8284095696327697658], [17072468807347166763, "base64", false, 7338540213148100975]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsecp256k1-7fec5b036683b716/dep-lib-libsecp256k1", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}