{"rustc": 383397013764560953, "features": "[]", "declared_features": "[]", "target": 5107985195345968876, "profile": 15657897354478470176, "path": 8181730235049894413, "deps": [[10570260326288551891, "solana_instruction", false, 6543215718921772342], [11091540729177102731, "solana_pubkey", false, 12981816345660999255]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-associated-token-account-client-ef5201eb3b451565/dep-lib-spl_associated_token_account_client", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}