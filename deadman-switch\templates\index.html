{% extends "base.html" %}

{% block title %}Trang chủ - Deadman Switch{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-heartbeat"></i> Trạng thái Deadman Switch</h4>
            </div>
            <div class="card-body">
                {% if not user_config %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Chưa cấu hình!</strong> 
                        Vui lòng <a href="{{ url_for('config_page') }}" class="alert-link">cấu hình số điện thoại khẩn cấp</a> trước khi sử dụng.
                    </div>
                {% else %}
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Ping cuối cùng:</h5>
                            {% if last_ping %}
                                <p class="text-success">
                                    <i class="fas fa-check-circle"></i>
                                    {{ last_ping.strftime('%d/%m/%Y %H:%M:%S') }}
                                </p>
                            {% else %}
                                <p class="text-warning">
                                    <i class="fas fa-exclamation-circle"></i>
                                    Chưa có ping nào
                                </p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5>Thời gian còn lại:</h5>
                            {% if time_remaining %}
                                {% if time_remaining.total_seconds() > 0 %}
                                    <p class="text-info" id="countdown">
                                        <i class="fas fa-clock"></i>
                                        <span id="time-remaining">{{ time_remaining }}</span>
                                    </p>
                                {% else %}
                                    <p class="text-danger">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>QUÁ HẠN!</strong> Cảnh báo đã được gửi
                                    </p>
                                {% endif %}
                            {% else %}
                                <p class="text-muted">Chưa có dữ liệu</p>
                            {% endif %}
                        </div>
                    </div>

                    <hr>

                    <div class="text-center">
                        <button id="ping-btn" class="btn btn-success btn-lg" onclick="sendPing()">
                            <i class="fas fa-hand-paper"></i> PING - Tôi an toàn!
                        </button>
                        <p class="text-muted mt-2">
                            Nhấn nút này để xác nhận bạn đang an toàn và làm mới thời gian
                        </p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-map-marker-alt"></i> Vị trí GPS</h5>
            </div>
            <div class="card-body">
                {% if latest_gps %}
                    <p><strong>Vĩ độ:</strong> {{ "%.6f"|format(latest_gps.latitude) }}</p>
                    <p><strong>Kinh độ:</strong> {{ "%.6f"|format(latest_gps.longitude) }}</p>
                    <p><strong>Cập nhật:</strong> {{ latest_gps.timestamp }}</p>
                    <p><strong>Nguồn:</strong> {{ latest_gps.source }}</p>
                    
                    <a href="https://maps.google.com/?q={{ latest_gps.latitude }},{{ latest_gps.longitude }}" 
                       target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt"></i> Xem trên bản đồ
                    </a>
                {% else %}
                    <p class="text-muted">Chưa có dữ liệu GPS</p>
                {% endif %}
                
                <hr>
                <button class="btn btn-outline-secondary btn-sm" onclick="updateGPS()">
                    <i class="fas fa-sync-alt"></i> Cập nhật GPS
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="getBrowserLocation()">
                    <i class="fas fa-crosshairs"></i> GPS từ trình duyệt
                </button>
            </div>
        </div>

        {% if user_config %}
        <div class="card mt-3">
            <div class="card-header bg-secondary text-white">
                <h6><i class="fas fa-info-circle"></i> Cấu hình hiện tại</h6>
            </div>
            <div class="card-body">
                <p><strong>SĐT khẩn cấp:</strong> {{ user_config.emergency_phone }}</p>
                <p><strong>Timeout:</strong> {{ user_config.timeout_hours }} giờ</p>
                <p><strong>GPS interval:</strong> {{ user_config.gps_interval_minutes }} phút</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Status indicator -->
<div class="fixed-bottom p-3">
    <div class="toast-container">
        <div id="status-toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">Trạng thái hệ thống</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="status-message">
                Đang kiểm tra...
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh status every 30 seconds
setInterval(updateStatus, 30000);
updateStatus(); // Initial load

function sendPing() {
    const btn = document.getElementById('ping-btn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang ping...';
    
    fetch('/ping', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showToast('Ping thành công! Thời gian đã được làm mới.', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('Lỗi ping: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('Lỗi kết nối: ' + error, 'error');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-hand-paper"></i> PING - Tôi an toàn!';
    });
}

function updateGPS() {
    fetch('/manual_gps', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showToast('GPS đã được cập nhật!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('Lỗi cập nhật GPS: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('Lỗi kết nối: ' + error, 'error');
    });
}

function getBrowserLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lon = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                // Send to server
                fetch('/manual_gps', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        latitude: lat,
                        longitude: lon,
                        accuracy: accuracy,
                        source: 'browser'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    showToast('GPS từ trình duyệt đã được cập nhật!', 'success');
                    setTimeout(() => location.reload(), 1000);
                });
            },
            function(error) {
                showToast('Không thể lấy vị trí từ trình duyệt: ' + error.message, 'error');
            }
        );
    } else {
        showToast('Trình duyệt không hỗ trợ geolocation', 'error');
    }
}

function updateStatus() {
    fetch('/status')
    .then(response => response.json())
    .then(data => {
        const message = document.getElementById('status-message');
        if (data.configured) {
            if (data.is_overdue) {
                message.innerHTML = '<i class="fas fa-exclamation-triangle text-danger"></i> QUÁ HẠN! Cảnh báo đã được gửi';
            } else if (data.time_remaining_seconds) {
                const hours = Math.floor(data.time_remaining_seconds / 3600);
                const minutes = Math.floor((data.time_remaining_seconds % 3600) / 60);
                message.innerHTML = `<i class="fas fa-clock text-info"></i> Còn ${hours}h ${minutes}m`;
            } else {
                message.innerHTML = '<i class="fas fa-check text-success"></i> Hệ thống hoạt động bình thường';
            }
        } else {
            message.innerHTML = '<i class="fas fa-exclamation-circle text-warning"></i> Chưa cấu hình';
        }
    })
    .catch(error => {
        document.getElementById('status-message').innerHTML = '<i class="fas fa-times text-danger"></i> Lỗi kết nối';
    });
}

function showToast(message, type) {
    const toast = document.getElementById('status-toast');
    const messageEl = document.getElementById('status-message');
    
    messageEl.innerHTML = message;
    toast.className = `toast ${type === 'success' ? 'bg-success' : 'bg-danger'} text-white`;
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}
</script>
{% endblock %}
