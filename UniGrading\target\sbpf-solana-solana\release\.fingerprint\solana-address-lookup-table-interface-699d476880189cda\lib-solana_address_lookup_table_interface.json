{"rustc": 15667804273984634056, "features": "[\"bincode\", \"bytemuck\", \"serde\"]", "declared_features": "[\"bincode\", \"bytemuck\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 16632371563014012789, "profile": 15369999826762902862, "path": 17081215787219869109, "deps": [[1053240057303801639, "bytemuck", false, 12293145985726688872], [3439881079477119364, "serde_derive", false, 8374348064459425518], [5356478757179768432, "solana_instruction", false, 4655203465458653505], [9917880067899433992, "solana_sdk_ids", false, 10530841290182252257], [10633404241517405153, "serde", false, 3616858324696967413], [12639858850933718058, "bincode", false, 2587281549695300938], [14148824040489233401, "solana_clock", false, 12298545431029543798], [14587049135498730917, "solana_slot_hashes", false, 1313578072931160838], [14901974103992976078, "solana_pubkey", false, 7804769177552396955]], "local": [{"CheckDepInfo": {"dep_info": "sbpf-solana-solana/release/.fingerprint/solana-address-lookup-table-interface-699d476880189cda/dep-lib-solana_address_lookup_table_interface", "checksum": false}}], "rustflags": ["-Zremap-cwd-prefix="], "metadata": 15841109435931148914, "config": 2202906307356721367, "compile_kind": 7175811312213930769}