# Deadman Switch Configuration
# Copy this file to .env and fill in your values

# Flask settings
SECRET_KEY=your-secret-key-here
DEBUG=True
HOST=localhost
PORT=5000

# Database
DATABASE_PATH=deadman.db

# GPS settings
GPS_INTERVAL_MINUTES=60
GPS_TIMEOUT_SECONDS=30

# Deadman switch settings
DEFAULT_TIMEOUT_HOURS=24
CHECK_INTERVAL_MINUTES=5

# Twilio SMS settings (optional - get free account at twilio.com)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Email settings (backup for SMS)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Example configurations:
# For Gmail: Use app password, not regular password
# For Outlook: smtp-mail.outlook.com, port 587
# For Yahoo: smtp.mail.yahoo.com, port 587
