{"rustc": 15667804273984634056, "features": "[\"borsh\"]", "declared_features": "[\"borsh\", \"serde-traits\"]", "target": 15447823234639250115, "profile": 4203826904601646399, "path": 17766274891394898364, "deps": [[1053240057303801639, "bytemuck", false, 12293145985726688872], [1055474869551399533, "solana_zk_sdk", false, 2727967339330363475], [1850668172878317325, "borsh", false, 1317031502576609190], [9011698338356143732, "solana_decode_error", false, 12805052131126446594], [10448766010662481490, "num_traits", false, 13040138947037519343], [11226986003760870092, "num_derive", false, 1381718671867750069], [12267894243814168928, "bytemuck_derive", false, 9700516147251732373], [12809794537881357198, "thiserror", false, 3796125151584735798], [13794841484170142166, "solana_msg", false, 8961793113024319731], [14901974103992976078, "solana_pubkey", false, 7804769177552396955], [16534834055546431096, "solana_program_option", false, 16438719378663337807], [16535291519090062424, "solana_program_error", false, 10603033257574202644]], "local": [{"CheckDepInfo": {"dep_info": "sbpf-solana-solana/release/.fingerprint/spl-pod-5f7587e189652886/dep-lib-spl_pod", "checksum": false}}], "rustflags": ["-Zremap-cwd-prefix="], "metadata": 11553053575385693802, "config": 2202906307356721367, "compile_kind": 7175811312213930769}