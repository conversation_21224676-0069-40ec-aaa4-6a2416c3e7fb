import sqlite3
import datetime
from typing import Optional, List, Tuple

class DeadmanDatabase:
    def __init__(self, db_path: str = "deadman.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Khởi tạo database và tạo các bảng cần thiết"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Bảng cấu hình người dùng
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_config (
                    id INTEGER PRIMARY KEY,
                    emergency_phone TEXT NOT NULL,
                    timeout_hours INTEGER DEFAULT 24,
                    gps_interval_minutes INTEGER DEFAULT 60,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Bảng lịch sử ping
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ping_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ping_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    user_agent TEXT
                )
            ''')
            
            # Bảng vị trí GPS
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS gps_locations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    latitude REAL NOT NULL,
                    longitude REAL NOT NULL,
                    accuracy REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    source TEXT DEFAULT 'auto'
                )
            ''')
            
            # Bảng lịch sử cảnh báo
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_type TEXT NOT NULL,
                    recipient TEXT NOT NULL,
                    message TEXT,
                    latitude REAL,
                    longitude REAL,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'pending'
                )
            ''')
            
            conn.commit()
    
    def get_user_config(self) -> Optional[dict]:
        """Lấy cấu hình người dùng"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM user_config ORDER BY id DESC LIMIT 1")
            row = cursor.fetchone()
            
            if row:
                return {
                    'id': row[0],
                    'emergency_phone': row[1],
                    'timeout_hours': row[2],
                    'gps_interval_minutes': row[3],
                    'created_at': row[4],
                    'updated_at': row[5]
                }
            return None
    
    def save_user_config(self, emergency_phone: str, timeout_hours: int = 24, 
                        gps_interval_minutes: int = 60):
        """Lưu cấu hình người dùng"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO user_config 
                (id, emergency_phone, timeout_hours, gps_interval_minutes, updated_at)
                VALUES (1, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (emergency_phone, timeout_hours, gps_interval_minutes))
            conn.commit()
    
    def record_ping(self, ip_address: str = None, user_agent: str = None):
        """Ghi lại ping từ người dùng"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO ping_history (ip_address, user_agent)
                VALUES (?, ?)
            ''', (ip_address, user_agent))
            conn.commit()
    
    def get_last_ping(self) -> Optional[datetime.datetime]:
        """Lấy thời gian ping cuối cùng"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT ping_time FROM ping_history ORDER BY ping_time DESC LIMIT 1")
            row = cursor.fetchone()
            
            if row:
                return datetime.datetime.fromisoformat(row[0])
            return None
    
    def save_gps_location(self, latitude: float, longitude: float, 
                         accuracy: float = None, source: str = 'auto'):
        """Lưu vị trí GPS"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO gps_locations (latitude, longitude, accuracy, source)
                VALUES (?, ?, ?, ?)
            ''', (latitude, longitude, accuracy, source))
            conn.commit()
    
    def get_latest_gps(self) -> Optional[dict]:
        """Lấy vị trí GPS mới nhất"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT latitude, longitude, accuracy, timestamp, source 
                FROM gps_locations 
                ORDER BY timestamp DESC LIMIT 1
            ''')
            row = cursor.fetchone()
            
            if row:
                return {
                    'latitude': row[0],
                    'longitude': row[1],
                    'accuracy': row[2],
                    'timestamp': row[3],
                    'source': row[4]
                }
            return None
    
    def record_alert(self, alert_type: str, recipient: str, message: str,
                    latitude: float = None, longitude: float = None):
        """Ghi lại cảnh báo đã gửi"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO alert_history 
                (alert_type, recipient, message, latitude, longitude)
                VALUES (?, ?, ?, ?, ?)
            ''', (alert_type, recipient, message, latitude, longitude))
            conn.commit()
    
    def update_alert_status(self, alert_id: int, status: str):
        """Cập nhật trạng thái cảnh báo"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE alert_history SET status = ? WHERE id = ?
            ''', (status, alert_id))
            conn.commit()
    
    def get_ping_history(self, limit: int = 50) -> List[dict]:
        """Lấy lịch sử ping"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT ping_time, ip_address, user_agent 
                FROM ping_history 
                ORDER BY ping_time DESC 
                LIMIT ?
            ''', (limit,))
            
            return [
                {
                    'ping_time': row[0],
                    'ip_address': row[1],
                    'user_agent': row[2]
                }
                for row in cursor.fetchall()
            ]
