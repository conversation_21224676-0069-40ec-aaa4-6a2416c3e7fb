{% extends "base.html" %}

{% block title %}L<PERSON><PERSON> sử - Deadman Switch{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-history"></i> <PERSON><PERSON><PERSON> sử Ping</h4>
            </div>
            <div class="card-body">
                {% if ping_history %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-clock"></i> Thời gian</th>
                                    <th><i class="fas fa-network-wired"></i> IP Address</th>
                                    <th><i class="fas fa-browser"></i> User Agent</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ping in ping_history %}
                                <tr>
                                    <td>{{ ping.ping_time }}</td>
                                    <td>{{ ping.ip_address or 'N/A' }}</td>
                                    <td>
                                        <small>{{ ping.user_agent[:50] + '...' if ping.user_agent and ping.user_agent|length > 50 else ping.user_agent or 'N/A' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            Hiển thị {{ ping_history|length }} ping gần nhất
                        </small>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Chưa có lịch sử ping</h5>
                        <p class="text-muted">Hãy thực hiện ping đầu tiên từ trang chủ</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-hand-paper"></i> Ping ngay
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-chart-line"></i> Thống kê</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-primary">{{ ping_history|length }}</h3>
                            <small class="text-muted">Tổng số ping</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-success">
                                {% if ping_history %}
                                    {{ ((now() - ping_history[0].ping_time|datetime).total_seconds() / 3600) | round(1) }}h
                                {% else %}
                                    N/A
                                {% endif %}
                            </h3>
                            <small class="text-muted">Ping cuối cách đây</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-info">
                                {% if ping_history|length > 1 %}
                                    {{ ((ping_history[0].ping_time|datetime - ping_history[-1].ping_time|datetime).total_seconds() / (ping_history|length - 1) / 3600) | round(1) }}h
                                {% else %}
                                    N/A
                                {% endif %}
                            </h3>
                            <small class="text-muted">Tần suất ping trung bình</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-warning">
                                {% set unique_ips = ping_history | map(attribute='ip_address') | unique | list %}
                                {{ unique_ips|length }}
                            </h3>
                            <small class="text-muted">Địa chỉ IP khác nhau</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
