{"rustc": 383397013764560953, "features": "[]", "declared_features": "[]", "target": 13909130799607605443, "profile": 15657897354478470176, "path": 266174234416511756, "deps": [[730322694175296062, "solana_address_lookup_table_interface", false, 11452944278534210259], [4043952427700520164, "solana_nonce", false, 12375832041855104643], [8611296141060937248, "solana_hash", false, 3782073555136251603], [9689903380558560274, "serde", false, 8537716410839897940], [10570260326288551891, "solana_instruction", false, 6543215718921772342], [10806645703491011684, "thiserror", false, 2270555407759382872], [11087274787214030812, "solana_keccak_hasher", false, 10010119295622958317], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [14591356476411885690, "solana_sdk_ids", false, 1263336053477103343], [15341883195918613377, "solana_system_interface", false, 7026953493720285368], [16041962814414187897, "solana_clock", false, 8585465566309786899], [16257276029081467297, "serde_derive", false, 110293047266484015], [16605237980896264354, "solana_message", false, 15027272176828544285]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-example-mocks-c179efa84eb7556c/dep-lib-solana_example_mocks", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}