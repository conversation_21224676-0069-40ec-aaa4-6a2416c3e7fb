#!/usr/bin/env python3
"""
Deadman Switch Travel Safety App
Launcher script with setup checks
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_python_version():
    """Kiểm tra Python version"""
    if sys.version_info < (3, 8):
        print("❌ Cần Python 3.8 trở lên")
        print(f"   Phiên bản hiện tại: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    return True

def check_dependencies():
    """Kiểm tra và cài đặt dependencies"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ Không tìm thấy requirements.txt")
        return False
    
    try:
        # Kiểm tra Flask
        import flask
        print("✅ Dependencies đã được cài đặt")
        return True
    except ImportError:
        print("📦 Đang cài đặt dependencies...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ Dependencies đã được cài đặt thành công")
            return True
        except subprocess.CalledProcessError:
            print("❌ Lỗi cài đặt dependencies")
            print("   Thử chạy: pip install -r requirements.txt")
            return False

def check_config():
    """Kiểm tra file cấu hình"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 Tạo file .env từ template...")
            env_file.write_text(env_example.read_text())
            print("✅ File .env đã được tạo")
        else:
            print("⚠️  Không có file cấu hình .env")
    else:
        print("✅ File cấu hình .env tồn tại")
    
    return True

def check_database():
    """Kiểm tra database"""
    db_file = Path("deadman.db")
    
    if db_file.exists():
        print("✅ Database đã tồn tại")
    else:
        print("📊 Database sẽ được tạo tự động")
    
    return True

def start_app():
    """Khởi động ứng dụng"""
    print("\n🚀 Đang khởi động Deadman Switch Travel Safety App...")
    print("   Nhấn Ctrl+C để dừng")
    
    try:
        # Import và chạy app
        from app import app, config
        
        # Mở browser sau 2 giây
        def open_browser():
            time.sleep(2)
            url = f"http://{config.HOST}:{config.PORT}"
            print(f"\n🌐 Mở trình duyệt: {url}")
            webbrowser.open(url)
        
        import threading
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # Chạy Flask app
        app.run(host=config.HOST, port=config.PORT, debug=config.DEBUG)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Đang tắt ứng dụng...")
        print("   Cảm ơn bạn đã sử dụng Deadman Switch!")
    except Exception as e:
        print(f"\n❌ Lỗi khởi động: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("=" * 50)
    print("🛡️  DEADMAN SWITCH TRAVEL SAFETY APP")
    print("=" * 50)
    print()
    
    # Kiểm tra hệ thống
    print("🔍 Kiểm tra hệ thống...")
    
    if not check_python_version():
        return 1
    
    if not check_dependencies():
        return 1
    
    if not check_config():
        return 1
    
    if not check_database():
        return 1
    
    print("\n✅ Tất cả kiểm tra đã hoàn thành!")
    print()
    
    # Hiển thị thông tin
    print("📋 THÔNG TIN QUAN TRỌNG:")
    print("   • Truy cập: http://localhost:5000")
    print("   • Cấu hình số điện thoại khẩn cấp trước khi sử dụng")
    print("   • Ping thường xuyên để tránh cảnh báo nhầm")
    print("   • Kiểm tra GPS và test hệ thống cảnh báo")
    print()
    
    # Khởi động app
    return 0 if start_app() else 1

if __name__ == "__main__":
    sys.exit(main())
