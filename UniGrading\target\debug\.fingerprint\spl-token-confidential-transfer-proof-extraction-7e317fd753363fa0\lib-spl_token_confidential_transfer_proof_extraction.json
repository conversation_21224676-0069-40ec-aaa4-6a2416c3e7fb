{"rustc": 383397013764560953, "features": "[]", "declared_features": "[]", "target": 9070344044293252602, "profile": 15657897354478470176, "path": 10690219740120816179, "deps": [[790673365560624081, "spl_pod", false, 17600426445546719696], [3438806654139177877, "solana_zk_sdk", false, 2684287058135583962], [6511429716036861196, "bytemuck", false, 9004261617411574230], [10806645703491011684, "thiserror", false, 2270555407759382872], [16016078550530309219, "solana_program", false, 3212434464576765496], [16234497081792451510, "solana_curve25519", false, 12953209395118152137]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-confidential-transfer-proof-extraction-7e317fd753363fa0/dep-lib-spl_token_confidential_transfer_proof_extraction", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}