{"rustc": 15667804273984634056, "features": "[\"derive\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-lang-idl\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\", \"interface-instructions\", \"lazy-account\"]", "target": 18429759200760939724, "profile": 4203826904601646399, "path": 9658085026979814940, "deps": [[1011640204279865735, "base64", false, 472559633229670969], [1053240057303801639, "bytemuck", false, 12293145985726688872], [1089310595235260188, "anchor_derive_serde", false, 3080039116976482722], [1772655304927281636, "anchor_attribute_program", false, 3239989324951582754], [3975022953004272744, "anchor_attribute_account", false, 16025166211608184041], [4255271538581701711, "solana_program", false, 8235818013309295696], [4786838005243155760, "anchor_attribute_access_control", false, 10349753224711022771], [5286331882899743471, "borsh", false, 1464770234283336558], [7809323047808687660, "anchor_derive_space", false, 8830691660029634440], [11266840602298992523, "thiserror", false, 12063351946184527781], [12043069111616805795, "anchor_attribute_event", false, 13801794735051048961], [12530562313707627918, "anchor_attribute_constant", false, 7796735395240585669], [12639858850933718058, "bincode", false, 2587281549695300938], [14915074800303460213, "anchor_attribute_error", false, 10143688209363224376], [15994092835900409492, "anchor_derive_accounts", false, 614245015646434943]], "local": [{"CheckDepInfo": {"dep_info": "sbpf-solana-solana/release/.fingerprint/anchor-lang-11a290656f83f740/dep-lib-anchor_lang", "checksum": false}}], "rustflags": ["-Zremap-cwd-prefix="], "metadata": 8904873174815195710, "config": 2202906307356721367, "compile_kind": 7175811312213930769}