{"rustc": 383397013764560953, "features": "[]", "declared_features": "[\"derive\"]", "target": 4936690485685825485, "profile": 15657897354478470176, "path": 98902792418252739, "deps": [[761872526999005629, "solana_msg", false, 14649943171307608157], [790673365560624081, "spl_pod", false, 17600426445546719696], [23*****************, "spl_discriminator", false, 18401927576032249778], [4145337479814240220, "solana_decode_error", false, 16033021158951676640], [4917153833802766511, "solana_program_error", false, 6377321729773093048], [5157631553186200874, "num_traits", false, 17203946164875098880], [6511429716036861196, "bytemuck", false, 9004261617411574230], [8008191657135824715, "thiserror", false, 16276810871065626392], [11263754829263059703, "num_derive", false, 10169912134563083283], [14666756292968957341, "solana_account_info", false, 13573356865800335666]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-type-length-value-cf723ce552e2a3b5/dep-lib-spl_type_length_value", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}