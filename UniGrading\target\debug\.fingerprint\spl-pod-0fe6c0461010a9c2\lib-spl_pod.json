{"rustc": 383397013764560953, "features": "[\"borsh\"]", "declared_features": "[\"borsh\", \"serde-traits\"]", "target": 1586917627076750936, "profile": 15657897354478470176, "path": 7135202749016025306, "deps": [[761872526999005629, "solana_msg", false, 14649943171307608157], [3438806654139177877, "solana_zk_sdk", false, 2684287058135583962], [4145337479814240220, "solana_decode_error", false, 16033021158951676640], [4917153833802766511, "solana_program_error", false, 6377321729773093048], [5157631553186200874, "num_traits", false, 17203946164875098880], [6203123018298125816, "borsh", false, 13850553873246415677], [6511429716036861196, "bytemuck", false, 9004261617411574230], [10806645703491011684, "thiserror", false, 2270555407759382872], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [11263754829263059703, "num_derive", false, 10169912134563083283], [13740259144933249371, "solana_program_option", false, 4827725718196140316], [15246557919602675095, "bytemuck_derive", false, 579081428122340601]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-pod-0fe6c0461010a9c2/dep-lib-spl_pod", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}