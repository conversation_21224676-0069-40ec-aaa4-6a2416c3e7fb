#!/usr/bin/env python3
"""
Simple tests for Deadman Switch app
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DeadmanDatabase
from gps_tracker import GPSTracker
from alert_service import AlertService

class TestDatabase(unittest.TestCase):
    def setUp(self):
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DeadmanDatabase(self.temp_db.name)
    
    def tearDown(self):
        # Clean up
        os.unlink(self.temp_db.name)
    
    def test_database_initialization(self):
        """Test database tables are created"""
        config = self.db.get_user_config()
        self.assertIsNone(config)  # Should be None initially
    
    def test_save_and_get_config(self):
        """Test saving and retrieving user config"""
        phone = "+84123456789"
        timeout = 24
        gps_interval = 60
        
        self.db.save_user_config(phone, timeout, gps_interval)
        config = self.db.get_user_config()
        
        self.assertIsNotNone(config)
        self.assertEqual(config['emergency_phone'], phone)
        self.assertEqual(config['timeout_hours'], timeout)
        self.assertEqual(config['gps_interval_minutes'], gps_interval)
    
    def test_ping_recording(self):
        """Test ping recording"""
        self.db.record_ping("127.0.0.1", "Test User Agent")
        
        last_ping = self.db.get_last_ping()
        self.assertIsNotNone(last_ping)
        
        history = self.db.get_ping_history(10)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['ip_address'], "127.0.0.1")
    
    def test_gps_recording(self):
        """Test GPS location recording"""
        lat, lon = 21.0285, 105.8542  # Hanoi coordinates
        
        self.db.save_gps_location(lat, lon, 100, 'test')
        
        latest_gps = self.db.get_latest_gps()
        self.assertIsNotNone(latest_gps)
        self.assertEqual(latest_gps['latitude'], lat)
        self.assertEqual(latest_gps['longitude'], lon)
        self.assertEqual(latest_gps['source'], 'test')

class TestGPSTracker(unittest.TestCase):
    def setUp(self):
        self.gps = GPSTracker()
    
    @patch('requests.get')
    def test_ip_geolocation(self, mock_get):
        """Test IP-based geolocation"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'latitude': 21.0285,
            'longitude': 105.8542,
            'city': 'Hanoi',
            'country_name': 'Vietnam'
        }
        mock_get.return_value = mock_response
        
        location = self.gps._get_location_by_ip()
        
        self.assertIsNotNone(location)
        self.assertEqual(location['latitude'], 21.0285)
        self.assertEqual(location['longitude'], 105.8542)
        self.assertEqual(location['source'], 'ip_geolocation')
    
    def test_browser_location_format(self):
        """Test browser location formatting"""
        lat, lon, accuracy = 21.0285, 105.8542, 50
        
        location = self.gps.get_location_from_browser(lat, lon, accuracy)
        
        self.assertEqual(location['latitude'], lat)
        self.assertEqual(location['longitude'], lon)
        self.assertEqual(location['accuracy'], accuracy)
        self.assertEqual(location['source'], 'browser_geolocation')
    
    def test_maps_url_generation(self):
        """Test Google Maps URL generation"""
        location = {
            'latitude': 21.0285,
            'longitude': 105.8542
        }
        
        url = self.gps.format_location_for_maps(location)
        expected = "https://maps.google.com/?q=21.0285,105.8542"
        
        self.assertEqual(url, expected)
    
    def test_distance_calculation(self):
        """Test distance calculation between two points"""
        hanoi = {'latitude': 21.0285, 'longitude': 105.8542}
        hcm = {'latitude': 10.8231, 'longitude': 106.6297}
        
        distance = self.gps.calculate_distance(hanoi, hcm)
        
        # Distance between Hanoi and Ho Chi Minh City is approximately 1160 km
        self.assertGreater(distance, 1100)
        self.assertLess(distance, 1200)

class TestAlertService(unittest.TestCase):
    def setUp(self):
        self.alert_service = AlertService()
    
    def test_emergency_message_formatting(self):
        """Test emergency message formatting"""
        gps_location = {
            'latitude': 21.0285,
            'longitude': 105.8542,
            'timestamp': '2024-01-01 12:00:00'
        }
        
        message = self.alert_service._format_emergency_message(24, gps_location)
        
        self.assertIn('21.0285', message)
        self.assertIn('105.8542', message)
        self.assertIn('24 giờ', message)
        self.assertIn('maps.google.com', message)
    
    def test_emergency_message_without_gps(self):
        """Test emergency message without GPS data"""
        message = self.alert_service._format_emergency_message(24, None)
        
        self.assertIn('24 giờ', message)
        self.assertIn('Không có thông tin GPS', message)
    
    @patch('requests.post')
    def test_textbelt_sms(self, mock_post):
        """Test TextBelt SMS sending"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'success': True}
        mock_post.return_value = mock_response
        
        # Test SMS sending (will use TextBelt since no Twilio config)
        result = self.alert_service._send_sms("+84123456789", "Test message")
        
        # Should attempt to send via TextBelt
        mock_post.assert_called()
        self.assertTrue(result)

def run_tests():
    """Run all tests"""
    print("🧪 Chạy tests cho Deadman Switch...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestDatabase))
    suite.addTests(loader.loadTestsFromTestCase(TestGPSTracker))
    suite.addTests(loader.loadTestsFromTestCase(TestAlertService))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ Tất cả tests đã pass!")
        return True
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
