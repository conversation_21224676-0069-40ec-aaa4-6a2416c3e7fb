from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import threading
import time
import datetime
from database import DeadmanDatabase
from config import config
from gps_tracker import GPSTracker
from alert_service import AlertService

app = Flask(__name__)
app.secret_key = config.SECRET_KEY

# Initialize services
db = DeadmanDatabase(config.DATABASE_PATH)
gps_tracker = GPSTracker()
alert_service = AlertService()

# Global variables
deadman_timer_thread = None
gps_tracker_thread = None
is_running = True

@app.route('/')
def index():
    """Trang chủ - hiển thị trạng thái và nút ping"""
    user_config = db.get_user_config()
    last_ping = db.get_last_ping()
    latest_gps = db.get_latest_gps()
    
    # Tính thời gian còn lại
    time_remaining = None
    if last_ping and user_config:
        timeout_delta = datetime.timedelta(hours=user_config['timeout_hours'])
        deadline = last_ping + timeout_delta
        time_remaining = deadline - datetime.datetime.now()
    
    return render_template('index.html', 
                         user_config=user_config,
                         last_ping=last_ping,
                         latest_gps=latest_gps,
                         time_remaining=time_remaining)

@app.route('/ping', methods=['POST'])
def ping():
    """Endpoint để ping - làm mới deadman timer"""
    try:
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent')
        
        db.record_ping(ip_address, user_agent)
        
        return jsonify({
            'status': 'success',
            'message': 'Ping recorded successfully',
            'timestamp': datetime.datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/config')
def config_page():
    """Trang cấu hình"""
    user_config = db.get_user_config()
    return render_template('config.html', config=user_config)

@app.route('/config', methods=['POST'])
def save_config():
    """Lưu cấu hình"""
    try:
        emergency_phone = request.form.get('emergency_phone')
        timeout_hours = int(request.form.get('timeout_hours', 24))
        gps_interval_minutes = int(request.form.get('gps_interval_minutes', 60))
        
        if not emergency_phone:
            flash('Số điện thoại khẩn cấp là bắt buộc!', 'error')
            return redirect(url_for('config_page'))
        
        db.save_user_config(emergency_phone, timeout_hours, gps_interval_minutes)
        flash('Cấu hình đã được lưu thành công!', 'success')
        
        return redirect(url_for('index'))
    except Exception as e:
        flash(f'Lỗi khi lưu cấu hình: {str(e)}', 'error')
        return redirect(url_for('config_page'))

@app.route('/status')
def status():
    """API endpoint trả về trạng thái hệ thống"""
    user_config = db.get_user_config()
    last_ping = db.get_last_ping()
    latest_gps = db.get_latest_gps()
    
    status_data = {
        'configured': user_config is not None,
        'last_ping': last_ping.isoformat() if last_ping else None,
        'latest_gps': latest_gps,
        'current_time': datetime.datetime.now().isoformat()
    }
    
    if last_ping and user_config:
        timeout_delta = datetime.timedelta(hours=user_config['timeout_hours'])
        deadline = last_ping + timeout_delta
        time_remaining = deadline - datetime.datetime.now()
        
        status_data.update({
            'deadline': deadline.isoformat(),
            'time_remaining_seconds': int(time_remaining.total_seconds()),
            'is_overdue': time_remaining.total_seconds() <= 0
        })
    
    return jsonify(status_data)

@app.route('/history')
def history():
    """Trang lịch sử ping và GPS"""
    ping_history = db.get_ping_history(50)
    return render_template('history.html', ping_history=ping_history)

@app.route('/manual_gps', methods=['POST'])
def manual_gps():
    """Endpoint để cập nhật GPS thủ công"""
    try:
        # Check if GPS data is provided in request (from browser)
        if request.is_json:
            data = request.get_json()
            location = {
                'latitude': data['latitude'],
                'longitude': data['longitude'],
                'accuracy': data.get('accuracy'),
                'source': data.get('source', 'manual')
            }
        else:
            # Get GPS from server-side services
            location = gps_tracker.get_current_location()

        if location:
            db.save_gps_location(
                location['latitude'],
                location['longitude'],
                location.get('accuracy'),
                location.get('source', 'manual')
            )
            return jsonify({
                'status': 'success',
                'location': location
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Không thể lấy vị trí GPS'
            }), 500
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/test_alert', methods=['POST'])
def test_alert():
    """Endpoint để gửi tin nhắn test"""
    try:
        user_config = db.get_user_config()
        if not user_config:
            return jsonify({
                'status': 'error',
                'message': 'Chưa cấu hình số điện thoại khẩn cấp'
            }), 400

        success = alert_service.send_test_alert(user_config['emergency_phone'])

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Tin nhắn test đã được gửi'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Không thể gửi tin nhắn test. Kiểm tra cấu hình SMS/Email.'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

def deadman_timer_worker():
    """Background worker kiểm tra deadman timer"""
    global is_running
    
    while is_running:
        try:
            user_config = db.get_user_config()
            if not user_config:
                time.sleep(config.CHECK_INTERVAL_MINUTES * 60)
                continue
            
            last_ping = db.get_last_ping()
            if not last_ping:
                time.sleep(config.CHECK_INTERVAL_MINUTES * 60)
                continue
            
            # Kiểm tra xem đã quá thời gian chưa
            timeout_delta = datetime.timedelta(hours=user_config['timeout_hours'])
            deadline = last_ping + timeout_delta
            
            if datetime.datetime.now() > deadline:
                # Kích hoạt cảnh báo
                latest_gps = db.get_latest_gps()
                alert_service.send_emergency_alert(
                    user_config['emergency_phone'],
                    user_config['timeout_hours'],
                    latest_gps
                )
                
                # Ghi lại cảnh báo
                db.record_alert(
                    'emergency',
                    user_config['emergency_phone'],
                    'Deadman switch activated',
                    latest_gps['latitude'] if latest_gps else None,
                    latest_gps['longitude'] if latest_gps else None
                )
                
                print(f"🚨 Emergency alert sent to {user_config['emergency_phone']}")
                
                # Chờ 1 giờ trước khi kiểm tra lại để tránh spam
                time.sleep(3600)
            else:
                time.sleep(config.CHECK_INTERVAL_MINUTES * 60)
                
        except Exception as e:
            print(f"Error in deadman timer worker: {e}")
            time.sleep(config.CHECK_INTERVAL_MINUTES * 60)

def gps_tracker_worker():
    """Background worker theo dõi GPS"""
    global is_running
    
    while is_running:
        try:
            user_config = db.get_user_config()
            if user_config:
                location = gps_tracker.get_current_location()
                if location:
                    db.save_gps_location(
                        location['latitude'],
                        location['longitude'],
                        location.get('accuracy'),
                        'auto'
                    )
                    print(f"📍 GPS updated: {location['latitude']}, {location['longitude']}")
                
                time.sleep(user_config['gps_interval_minutes'] * 60)
            else:
                time.sleep(config.GPS_INTERVAL_MINUTES * 60)
                
        except Exception as e:
            print(f"Error in GPS tracker worker: {e}")
            time.sleep(config.GPS_INTERVAL_MINUTES * 60)

def start_background_services():
    """Khởi động các background services"""
    global deadman_timer_thread, gps_tracker_thread
    
    # Khởi động deadman timer
    deadman_timer_thread = threading.Thread(target=deadman_timer_worker, daemon=True)
    deadman_timer_thread.start()
    
    # Khởi động GPS tracker
    gps_tracker_thread = threading.Thread(target=gps_tracker_worker, daemon=True)
    gps_tracker_thread.start()
    
    print("✅ Background services started")

if __name__ == '__main__':
    print("🚀 Starting Deadman Switch Travel Safety App...")
    start_background_services()
    
    try:
        app.run(host=config.HOST, port=config.PORT, debug=config.DEBUG)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        is_running = False
