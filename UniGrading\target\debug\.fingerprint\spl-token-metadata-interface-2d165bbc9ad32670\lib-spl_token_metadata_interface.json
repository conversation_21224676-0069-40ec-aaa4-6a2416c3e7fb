{"rustc": 383397013764560953, "features": "[]", "declared_features": "[\"serde-traits\"]", "target": 1169884983282116594, "profile": 15657897354478470176, "path": 5885229778571061047, "deps": [[761872526999005629, "solana_msg", false, 14649943171307608157], [790673365560624081, "spl_pod", false, 17600426445546719696], [2348231670893597196, "spl_discriminator", false, 18401927576032249778], [4145337479814240220, "solana_decode_error", false, 16033021158951676640], [4917153833802766511, "solana_program_error", false, 6377321729773093048], [5157631553186200874, "num_traits", false, 17203946164875098880], [6203123018298125816, "borsh", false, 13850553873246415677], [8008191657135824715, "thiserror", false, 16276810871065626392], [10570260326288551891, "solana_instruction", false, 6543215718921772342], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [11263754829263059703, "num_derive", false, 10169912134563083283], [13937246957939922657, "spl_type_length_value", false, 4719055570891159530], [17154729538425362871, "solana_borsh", false, 6827307904907362361]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-metadata-interface-2d165bbc9ad32670/dep-lib-spl_token_metadata_interface", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}