{"rustc": 383397013764560953, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9241925498456048256, "build_script_build", false, 8155700789046305076]], "local": [{"RerunIfChanged": {"output": "debug/build/blake3-cf2b9fbf2f5aa986/output", "paths": ["c/CMakePresets.json", "c/blake3_avx2_x86-64_windows_gnu.S", "c/Makefile.testing", "c/test.py", "c/blake3_sse41_x86-64_windows_gnu.S", "c/blake3_dispatch.c", "c/blake3_avx512_x86-64_unix.S", "c/blake3_avx512.c", "c/README.md", "c/blake3-config.cmake.in", "c/example_tbb.c", "c/libblake3.pc.in", "c/blake3_avx512_x86-64_windows_msvc.asm", "c/blake3.c", "c/main.c", "c/blake3_neon.c", "c/blake3_avx2_x86-64_windows_msvc.asm", "c/blake3_avx2_x86-64_unix.S", "c/.giti<PERSON>re", "c/example.c", "c/blake3_sse2_x86-64_unix.S", "c/blake3_sse2.c", "c/blake3_sse41_x86-64_windows_msvc.asm", "c/blake3_avx512_x86-64_windows_gnu.S", "c/blake3_tbb.cpp", "c/blake3_sse41.c", "c/CMakeLists.txt", "c/blake3_impl.h", "c/blake3.h", "c/dependencies", "c/blake3_portable.c", "c/blake3_avx2.c", "c/blake3_sse2_x86-64_windows_gnu.S", "c/cmake", "c/blake3_sse2_x86-64_windows_msvc.asm", "c/blake3_sse41_x86-64_unix.S"]}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 0, "compile_kind": 0}