// Main JavaScript for Deadman Switch app

// Global variables
let statusUpdateInterval;
let countdownInterval;

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Start status updates
    startStatusUpdates();
    
    // Initialize countdown if on main page
    if (document.getElementById('time-remaining')) {
        startCountdown();
    }
    
    // Add keyboard shortcuts
    addKeyboardShortcuts();
    
    // Check for notifications permission
    requestNotificationPermission();
}

function startStatusUpdates() {
    // Update status immediately
    updateSystemStatus();
    
    // Then update every 30 seconds
    statusUpdateInterval = setInterval(updateSystemStatus, 30000);
}

function updateSystemStatus() {
    fetch('/status')
        .then(response => response.json())
        .then(data => {
            updateStatusIndicators(data);
            updateCountdownDisplay(data);
        })
        .catch(error => {
            console.error('Status update failed:', error);
            showNotification('Lỗi kết nối hệ thống', 'error');
        });
}

function updateStatusIndicators(data) {
    // Update any status indicators on the page
    const statusElements = document.querySelectorAll('[data-status]');
    
    statusElements.forEach(element => {
        const statusType = element.getAttribute('data-status');
        
        switch(statusType) {
            case 'connection':
                element.className = 'badge bg-success';
                element.textContent = 'Kết nối';
                break;
            case 'configuration':
                if (data.configured) {
                    element.className = 'badge bg-success';
                    element.textContent = 'Đã cấu hình';
                } else {
                    element.className = 'badge bg-warning';
                    element.textContent = 'Chưa cấu hình';
                }
                break;
            case 'deadman':
                if (data.is_overdue) {
                    element.className = 'badge bg-danger';
                    element.textContent = 'Quá hạn';
                } else if (data.time_remaining_seconds) {
                    element.className = 'badge bg-success';
                    element.textContent = 'Hoạt động';
                } else {
                    element.className = 'badge bg-secondary';
                    element.textContent = 'Chưa ping';
                }
                break;
        }
    });
}

function updateCountdownDisplay(data) {
    const countdownElement = document.getElementById('time-remaining');
    
    if (countdownElement && data.time_remaining_seconds) {
        const seconds = data.time_remaining_seconds;
        
        if (seconds > 0) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            countdownElement.textContent = `${hours}h ${minutes}m ${secs}s`;
            
            // Change color based on time remaining
            const parent = countdownElement.closest('p');
            if (hours < 2) {
                parent.className = 'text-danger';
            } else if (hours < 6) {
                parent.className = 'text-warning';
            } else {
                parent.className = 'text-info';
            }
        } else {
            countdownElement.textContent = 'QUÁ HẠN!';
            countdownElement.closest('p').className = 'text-danger';
        }
    }
}

function startCountdown() {
    countdownInterval = setInterval(() => {
        const countdownElement = document.getElementById('time-remaining');
        if (countdownElement) {
            // This will be updated by the status update function
            // Just ensure the interval is running
        }
    }, 1000);
}

// Ping function
async function sendPing() {
    const btn = document.getElementById('ping-btn');
    if (!btn) return;
    
    // Disable button and show loading
    btn.disabled = true;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang ping...';
    
    try {
        const response = await fetch('/ping', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showNotification('Ping thành công! Thời gian đã được làm mới.', 'success');
            
            // Update UI immediately
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Lỗi ping: ' + data.message, 'error');
        }
    } catch (error) {
        showNotification('Lỗi kết nối: ' + error.message, 'error');
    } finally {
        // Restore button
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// GPS functions
async function updateGPS() {
    try {
        const response = await fetch('/manual_gps', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showNotification('GPS đã được cập nhật!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Lỗi cập nhật GPS: ' + data.message, 'error');
        }
    } catch (error) {
        showNotification('Lỗi kết nối: ' + error.message, 'error');
    }
}

function getBrowserLocation() {
    if (!navigator.geolocation) {
        showNotification('Trình duyệt không hỗ trợ geolocation', 'error');
        return;
    }
    
    showNotification('Đang lấy vị trí từ trình duyệt...', 'info');
    
    navigator.geolocation.getCurrentPosition(
        async function(position) {
            const lat = position.coords.latitude;
            const lon = position.coords.longitude;
            const accuracy = position.coords.accuracy;
            
            try {
                const response = await fetch('/manual_gps', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        latitude: lat,
                        longitude: lon,
                        accuracy: accuracy,
                        source: 'browser'
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    showNotification('GPS từ trình duyệt đã được cập nhật!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('Lỗi lưu GPS: ' + data.message, 'error');
                }
            } catch (error) {
                showNotification('Lỗi gửi GPS: ' + error.message, 'error');
            }
        },
        function(error) {
            let message = 'Không thể lấy vị trí: ';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    message += 'Người dùng từ chối chia sẻ vị trí';
                    break;
                case error.POSITION_UNAVAILABLE:
                    message += 'Thông tin vị trí không khả dụng';
                    break;
                case error.TIMEOUT:
                    message += 'Timeout khi lấy vị trí';
                    break;
                default:
                    message += 'Lỗi không xác định';
                    break;
            }
            showNotification(message, 'error');
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        }
    );
}

// Notification functions
function showNotification(message, type = 'info') {
    // Try to use browser notifications first
    if (Notification.permission === 'granted') {
        new Notification('Deadman Switch', {
            body: message,
            icon: '/static/icon.png'
        });
    }
    
    // Also show toast notification
    showToast(message, type);
}

function showToast(message, type = 'info') {
    // Create toast element if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast
    const toastId = 'toast-' + Date.now();
    const toastHTML = `
        <div id="${toastId}" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-${getIconForType(type)} me-2"></i>
                <strong class="me-auto">Deadman Switch</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    
    // Set toast color based on type
    toastElement.className += ` bg-${getBootstrapColorForType(type)} text-white`;
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function getIconForType(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        case 'info': return 'info-circle';
        default: return 'info-circle';
    }
}

function getBootstrapColorForType(type) {
    switch(type) {
        case 'success': return 'success';
        case 'error': return 'danger';
        case 'warning': return 'warning';
        case 'info': return 'info';
        default: return 'primary';
    }
}

// Keyboard shortcuts
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Ctrl+P or Cmd+P for ping
        if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
            event.preventDefault();
            const pingBtn = document.getElementById('ping-btn');
            if (pingBtn && !pingBtn.disabled) {
                sendPing();
            }
        }
        
        // Ctrl+G or Cmd+G for GPS update
        if ((event.ctrlKey || event.metaKey) && event.key === 'g') {
            event.preventDefault();
            updateGPS();
        }
    });
}

// Request notification permission
function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (statusUpdateInterval) {
        clearInterval(statusUpdateInterval);
    }
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
});

// Service worker registration (for offline support)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/static/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}
