{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON> hình - Deadman Switch{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-cog"></i> C<PERSON>u hình Deadman Switch</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="emergency_phone" class="form-label">
                            <i class="fas fa-phone"></i> Số điện thoại khẩn cấp *
                        </label>
                        <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" 
                               value="{{ config.emergency_phone if config else '' }}" 
                               placeholder="+84123456789 hoặc 0123456789" required>
                        <div class="form-text">
                            Số điện thoại sẽ nhận SMS cảnh báo khi deadman switch đ<PERSON><PERSON><PERSON> kích ho<PERSON>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="timeout_hours" class="form-label">
                            <i class="fas fa-clock"></i> Thời gian timeout (giờ)
                        </label>
                        <input type="number" class="form-control" id="timeout_hours" name="timeout_hours" 
                               value="{{ config.timeout_hours if config else 24 }}" 
                               min="1" max="168" required>
                        <div class="form-text">
                            Số giờ không ping trước khi gửi cảnh báo (1-168 giờ, mặc định 24 giờ)
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="gps_interval_minutes" class="form-label">
                            <i class="fas fa-map-marker-alt"></i> Tần suất cập nhật GPS (phút)
                        </label>
                        <input type="number" class="form-control" id="gps_interval_minutes" name="gps_interval_minutes" 
                               value="{{ config.gps_interval_minutes if config else 60 }}" 
                               min="5" max="1440" required>
                        <div class="form-text">
                            Tự động cập nhật GPS mỗi X phút (5-1440 phút, mặc định 60 phút)
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Lưu cấu hình
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Test section -->
        {% if config %}
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-vial"></i> Kiểm tra hệ thống</h5>
            </div>
            <div class="card-body">
                <p>Gửi tin nhắn test để kiểm tra hệ thống cảnh báo hoạt động:</p>
                <button class="btn btn-outline-info" onclick="sendTestAlert()">
                    <i class="fas fa-paper-plane"></i> Gửi tin nhắn test
                </button>
                <div id="test-result" class="mt-2"></div>
            </div>
        </div>
        {% endif %}

        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-info-circle"></i> Hướng dẫn sử dụng</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Cấu hình số điện thoại khẩn cấp:</strong> Nhập số điện thoại sẽ nhận cảnh báo</li>
                    <li><strong>Đặt thời gian timeout:</strong> Thời gian tối đa không ping trước khi cảnh báo</li>
                    <li><strong>Ping thường xuyên:</strong> Truy cập trang chủ và nhấn nút PING để xác nhận an toàn</li>
                    <li><strong>GPS tự động:</strong> Hệ thống tự động cập nhật vị trí theo tần suất đã đặt</li>
                    <li><strong>Cảnh báo khẩn cấp:</strong> Nếu quá thời gian không ping, SMS sẽ được gửi kèm GPS cuối</li>
                </ol>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Mẹo:</strong> Đặt bookmark trang chủ trên điện thoại để ping nhanh chóng khi cần
                </div>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Lưu ý:</strong> Hệ thống sử dụng IP geolocation nên GPS có thể không chính xác 100%. 
                    Sử dụng "GPS từ trình duyệt" để có vị trí chính xác hơn.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function sendTestAlert() {
    const btn = event.target;
    const resultDiv = document.getElementById('test-result');
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang gửi...';
    
    fetch('/test_alert', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            resultDiv.innerHTML = '<div class="alert alert-success">✅ Tin nhắn test đã được gửi!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger">❌ Lỗi: ' + data.message + '</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger">❌ Lỗi kết nối: ' + error + '</div>';
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-paper-plane"></i> Gửi tin nhắn test';
    });
}
</script>
{% endblock %}
