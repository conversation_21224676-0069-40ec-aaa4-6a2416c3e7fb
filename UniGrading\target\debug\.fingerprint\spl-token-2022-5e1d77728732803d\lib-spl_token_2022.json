{"rustc": 383397013764560953, "features": "[\"default\", \"no-entrypoint\", \"zk-ops\"]", "declared_features": "[\"default\", \"no-entrypoint\", \"serde-traits\", \"test-sbf\", \"zk-ops\"]", "target": 2664260296053320942, "profile": 6652793396284126538, "path": 9236744961709543897, "deps": [[790673365560624081, "spl_pod", false, 17600426445546719696], [3438806654139177877, "solana_zk_sdk", false, 2684287058135583962], [5157631553186200874, "num_traits", false, 17203946164875098880], [6511429716036861196, "bytemuck", false, 9004261617411574230], [7862880122426466754, "spl_elgamal_registry", false, 6606100629455376000], [8008191657135824715, "thiserror", false, 16276810871065626392], [9453905076908448987, "spl_token_confidential_transfer_proof_generation", false, 14921187804639195074], [9529943735784919782, "arrayref", false, 3101610639674147938], [9986636203687404999, "spl_transfer_hook_interface", false, 12496820936613558949], [11263754829263059703, "num_derive", false, 10169912134563083283], [11885291781139204510, "spl_memo", false, 7427201102155355709], [12682673687743740477, "spl_token", false, 18148318013240414305], [13155245355270259291, "spl_token_confidential_transfer_proof_extraction", false, 18214294317205124269], [13937246957939922657, "spl_type_length_value", false, 4719055570891159530], [16016078550530309219, "solana_program", false, 3212434464576765496], [16712258961403650142, "num_enum", false, 1395736475570976987], [17340930586486050809, "spl_token_group_interface", false, 9879314859717878116], [17667569856882013889, "spl_token_metadata_interface", false, 6701156436304463209], [17899936559789965257, "spl_token_confidential_transfer_ciphertext_arithmetic", false, 4718526709576878134], [17909568817133603617, "solana_security_txt", false, 17749637099800122919]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-2022-5e1d77728732803d/dep-lib-spl_token_2022", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}