{"rustc": 383397013764560953, "features": "[\"cargo_toml\", \"hash\", \"idl-build\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"cargo_toml\", \"event-cpi\", \"hash\", \"idl-build\", \"init-if-needed\", \"interface-instructions\"]", "target": 17778334149744802995, "profile": 12878658482865712, "path": 18350213797211743555, "deps": [[2713742371683562785, "syn", false, 5617923075506449886], [3060637413840920116, "proc_macro2", false, 10661881203608513514], [6170788409352141399, "cargo_toml", false, 6876351131683449082], [6616501577376279788, "bs58", false, 4352592699612918395], [8008191657135824715, "thiserror", false, 16276810871065626392], [9689903380558560274, "serde", false, 8537716410839897940], [9857275760291862238, "sha2", false, 15286038725805067178], [13625485746686963219, "anyhow", false, 16408017755803223262], [15367738274754116744, "serde_json", false, 18216465132187117326], [16131248048418321657, "heck", false, 16574158362988723564], [17990358020177143287, "quote", false, 12379506894038705714]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-syn-91c5474bc3c9731c/dep-lib-anchor_syn", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}