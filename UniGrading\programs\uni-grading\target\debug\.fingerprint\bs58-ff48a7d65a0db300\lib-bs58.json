{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 2225463790103693989, "path": 3137287978104977033, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bs58-ff48a7d65a0db300\\dep-lib-bs58", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}