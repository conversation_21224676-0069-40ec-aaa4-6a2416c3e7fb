{"rustc": 383397013764560953, "features": "[\"bincode\", \"blake3\", \"serde\"]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 7288017095310012413, "profile": 6652793396284126538, "path": 2204154313452959754, "deps": [[65234016722529558, "bincode", false, 12460930568304774582], [4113188218898653100, "solana_short_vec", false, 2991117844169968202], [8611296141060937248, "solana_hash", false, 3782073555136251603], [9241925498456048256, "blake3", false, 6574870521149759371], [9556858120010252096, "solana_transaction_error", false, 16812687930414739825], [9689903380558560274, "serde", false, 8537716410839897940], [10570260326288551891, "solana_instruction", false, 6543215718921772342], [11091540729177102731, "solana_pubkey", false, 12981816345660999255], [11702702251883620295, "solana_bincode", false, 14972958386282109995], [14591356476411885690, "solana_sdk_ids", false, 1263336053477103343], [15341883195918613377, "solana_system_interface", false, 7026953493720285368], [15429715045911386410, "solana_sanitize", false, 4200197951219190093], [16257276029081467297, "serde_derive", false, 110293047266484015], [17917672826516349275, "lazy_static", false, 7702015489672445363]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-message-19b12f9bb37dd2a3/dep-lib-solana_message", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}