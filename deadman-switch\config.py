import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class Config:
    # Flask settings
    SECRET_KEY: str = "deadman-switch-secret-key-change-in-production"
    DEBUG: bool = True
    HOST: str = "localhost"
    PORT: int = 5000
    
    # Database settings
    DATABASE_PATH: str = "deadman.db"
    
    # GPS settings
    GPS_INTERVAL_MINUTES: int = 60  # Gửi GPS mỗi 60 phút
    GPS_TIMEOUT_SECONDS: int = 30   # Timeout khi lấy GPS
    
    # Deadman switch settings
    DEFAULT_TIMEOUT_HOURS: int = 24  # Mặc định 24 giờ không ping thì cảnh báo
    CHECK_INTERVAL_MINUTES: int = 5  # Kiểm tra deadman timer mỗi 5 phút
    
    # SMS/Alert settings
    TWILIO_ACCOUNT_SID: Optional[str] = None
    TWILIO_AUTH_TOKEN: Optional[str] = None
    TWILIO_PHONE_NUMBER: Optional[str] = None
    
    # Email settings (backup cho SMS)
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    EMAIL_ADDRESS: Optional[str] = None
    EMAIL_PASSWORD: Optional[str] = None
    
    # Emergency settings
    EMERGENCY_MESSAGE_TEMPLATE: str = """
🚨 CẢNH BÁO KHẨN CẤP - DEADMAN SWITCH ACTIVATED 🚨

Người dùng đã không phản hồi trong {timeout_hours} giờ.
Vị trí GPS cuối cùng: {latitude}, {longitude}
Thời gian GPS: {gps_time}
Google Maps: https://maps.google.com/?q={latitude},{longitude}

Vui lòng kiểm tra an toàn ngay lập tức!
"""
    
    @classmethod
    def from_env(cls):
        """Tạo config từ environment variables"""
        return cls(
            SECRET_KEY=os.getenv("SECRET_KEY", cls.SECRET_KEY),
            DEBUG=os.getenv("DEBUG", "True").lower() == "true",
            HOST=os.getenv("HOST", cls.HOST),
            PORT=int(os.getenv("PORT", cls.PORT)),
            
            DATABASE_PATH=os.getenv("DATABASE_PATH", cls.DATABASE_PATH),
            
            GPS_INTERVAL_MINUTES=int(os.getenv("GPS_INTERVAL_MINUTES", cls.GPS_INTERVAL_MINUTES)),
            GPS_TIMEOUT_SECONDS=int(os.getenv("GPS_TIMEOUT_SECONDS", cls.GPS_TIMEOUT_SECONDS)),
            
            DEFAULT_TIMEOUT_HOURS=int(os.getenv("DEFAULT_TIMEOUT_HOURS", cls.DEFAULT_TIMEOUT_HOURS)),
            CHECK_INTERVAL_MINUTES=int(os.getenv("CHECK_INTERVAL_MINUTES", cls.CHECK_INTERVAL_MINUTES)),
            
            TWILIO_ACCOUNT_SID=os.getenv("TWILIO_ACCOUNT_SID"),
            TWILIO_AUTH_TOKEN=os.getenv("TWILIO_AUTH_TOKEN"),
            TWILIO_PHONE_NUMBER=os.getenv("TWILIO_PHONE_NUMBER"),
            
            SMTP_SERVER=os.getenv("SMTP_SERVER", cls.SMTP_SERVER),
            SMTP_PORT=int(os.getenv("SMTP_PORT", cls.SMTP_PORT)),
            EMAIL_ADDRESS=os.getenv("EMAIL_ADDRESS"),
            EMAIL_PASSWORD=os.getenv("EMAIL_PASSWORD"),
        )

# Global config instance
config = Config.from_env()
