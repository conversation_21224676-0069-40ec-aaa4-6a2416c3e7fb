/* Custom styles for Deadman Switch app */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
    margin-bottom: 1rem;
}

.card-header {
    font-weight: 600;
}

/* Ping button styling */
#ping-btn {
    font-size: 1.2rem;
    padding: 15px 30px;
    border-radius: 50px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#ping-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

#ping-btn:active {
    transform: translateY(0);
}

/* Status indicators */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* Countdown timer */
#countdown {
    font-size: 1.1rem;
    font-weight: bold;
}

/* GPS coordinates */
.gps-coords {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 5px;
    border-radius: 3px;
}

/* Toast notifications */
.toast {
    min-width: 300px;
}

/* Status cards */
.status-card {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.status-card.success {
    border-left-color: #28a745;
}

.status-card.warning {
    border-left-color: #ffc107;
}

.status-card.danger {
    border-left-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    #ping-btn {
        font-size: 1rem;
        padding: 12px 24px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Loading spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Alert styling */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Button improvements */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

/* Table styling */
.table {
    background-color: white;
}

.table th {
    border-top: none;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Form styling */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
}

/* Statistics cards */
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Emergency styling */
.emergency-alert {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Success styling */
.success-indicator {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 50px;
    padding: 10px 20px;
    display: inline-block;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #121212;
        color: #ffffff;
    }
    
    .card {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    .table {
        background-color: #1e1e1e;
        color: #ffffff;
    }
    
    .table th {
        background-color: #333;
        color: #ffffff;
    }
}
