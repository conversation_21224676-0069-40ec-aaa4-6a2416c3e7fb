# Deadman Switch Travel Safety App

Ứng dụng an toàn du lịch với tính năng deadman switch - gử<PERSON> cảnh báo khẩn cấp khi người dùng không ping trong thời gian quy định.

## Tính năng chính

- **GPS Tracking**: Tự động gửi vị trí GPS lên server mỗi tiếng
- **Web Ping Interface**: Ping qua web browser để xác nhận an toàn  
- **Flexible Timer**: <PERSON> bất cứ lúc nào để làm mới thời gian
- **Emergency Alert**: Tự động gửi SMS cảnh báo kèm GPS cuối cùng
- **Simple Setup**: Chỉ cần một số điện thoại khẩn cấp

## Kiến trúc hệ thống

```
[Desktop App] ←→ [Local Web Server] ←→ [SQLite DB]
     ↓                    ↓
[GPS Service]    [SMS Service (Free Tier)]
```

## Tech Stack

- **Backend**: Python Flask (đơn gi<PERSON>, miễn phí)
- **Database**: SQLite (local, không cần cloud)
- **Frontend**: HTML/CSS/JavaScript (web interface)
- **GPS**: Python geolocation libraries  
- **SMS**: Twilio Free Tier hoặc email alerts
- **Deployment**: Local desktop application

## Cấu trúc thư mục

```
deadman-switch/
├── app.py              # Flask server chính
├── gps_tracker.py      # GPS tracking service
├── alert_service.py    # SMS/Email alert service
├── database.py         # SQLite database operations
├── config.py           # Configuration settings
├── static/             # CSS, JS files
├── templates/          # HTML templates
├── requirements.txt    # Python dependencies
└── deadman.db         # SQLite database file
```

## Cài đặt và chạy

### Bước 1: Chuẩn bị môi trường

1. **Cài đặt Python 3.8+**
   ```bash
   python --version  # Kiểm tra version
   ```

2. **Clone hoặc tải project**
   ```bash
   cd deadman-switch
   ```

3. **Tạo virtual environment (khuyến nghị)**
   ```bash
   python -m venv venv

   # Windows
   venv\Scripts\activate

   # Linux/Mac
   source venv/bin/activate
   ```

### Bước 2: Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### Bước 3: Cấu hình

1. **Copy file cấu hình**
   ```bash
   cp .env.example .env
   ```

2. **Chỉnh sửa file .env** (tùy chọn)
   - Cấu hình Twilio cho SMS (miễn phí $15 credit)
   - Hoặc cấu hình email để gửi cảnh báo

### Bước 4: Chạy ứng dụng

```bash
python app.py
```

### Bước 5: Truy cập web interface

Mở trình duyệt và truy cập: `http://localhost:5000`

## Hướng dẫn sử dụng

### Lần đầu sử dụng

1. **Cấu hình số điện thoại khẩn cấp**
   - Truy cập: `http://localhost:5000/config`
   - Nhập số điện thoại sẽ nhận cảnh báo
   - Đặt thời gian timeout (khuyến nghị: 24-48 giờ)

2. **Test hệ thống**
   - Nhấn "Gửi tin nhắn test" để kiểm tra
   - Nếu không nhận được SMS, hệ thống sẽ gửi email

3. **Ping đầu tiên**
   - Quay về trang chủ
   - Nhấn nút "PING - Tôi an toàn!"

### Sử dụng hàng ngày

1. **Ping thường xuyên**: Truy cập `http://localhost:5000` và ping
2. **Kiểm tra GPS**: Vị trí sẽ được cập nhật tự động
3. **Xem lịch sử**: Theo dõi các lần ping trước đó

## Cấu hình nâng cao

### SMS qua Twilio (Khuyến nghị)

1. Đăng ký tài khoản miễn phí tại [twilio.com](https://twilio.com)
2. Lấy Account SID, Auth Token, và Phone Number
3. Cập nhật file `.env`:
   ```
   TWILIO_ACCOUNT_SID=your_account_sid
   TWILIO_AUTH_TOKEN=your_auth_token
   TWILIO_PHONE_NUMBER=+**********
   ```

### Email backup

1. Sử dụng Gmail với App Password:
   ```
   EMAIL_ADDRESS=<EMAIL>
   EMAIL_PASSWORD=your_app_password
   ```

2. Hoặc sử dụng SMTP server khác:
   ```
   SMTP_SERVER=smtp.your-provider.com
   SMTP_PORT=587
   ```
