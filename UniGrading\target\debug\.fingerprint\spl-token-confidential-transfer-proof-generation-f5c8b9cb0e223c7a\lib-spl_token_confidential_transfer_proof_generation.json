{"rustc": 383397013764560953, "features": "[]", "declared_features": "[]", "target": 1817782919825969750, "profile": 6652793396284126538, "path": 4853458812791352067, "deps": [[3438806654139177877, "solana_zk_sdk", false, 2684287058135583962], [8008191657135824715, "thiserror", false, 16276810871065626392], [13595581133353633439, "curve25519_dalek", false, 13059955556073542927]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-confidential-transfer-proof-generation-f5c8b9cb0e223c7a/dep-lib-spl_token_confidential_transfer_proof_generation", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}