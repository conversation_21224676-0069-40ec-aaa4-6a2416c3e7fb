{"rustc": 383397013764560953, "features": "[\"borsh-derive\", \"default\", \"derive\", \"std\", \"unstable__schema\"]", "declared_features": "[\"ascii\", \"borsh-derive\", \"bson\", \"bytes\", \"de_strict_order\", \"default\", \"derive\", \"hashbrown\", \"indexmap\", \"rc\", \"std\", \"unstable__schema\"]", "target": 4760962088884618199, "profile": 15657897354478470176, "path": 5887662035533075996, "deps": [[5176760276063461045, "borsh_derive", false, 15736647868972425431], [6203123018298125816, "build_script_build", false, 626508307860542289]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/borsh-ff2ba71d66248b1e/dep-lib-borsh", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}