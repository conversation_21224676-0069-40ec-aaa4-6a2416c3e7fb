{"rustc": 15667804273984634056, "features": "[\"hash\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"cargo_toml\", \"event-cpi\", \"hash\", \"idl-build\", \"init-if-needed\", \"interface-instructions\"]", "target": 6906961968906685573, "profile": 261089101934345608, "path": 9883388960071662960, "deps": [[1954840786535925237, "bs58", false, 5101657937324326207], [5123194605536730508, "sha2", false, 7280211909939518813], [10291739091677281249, "anyhow", false, 7254203674522536592], [10633404241517405153, "serde", false, 14508304126200205155], [11266840602298992523, "thiserror", false, 2936485374218286943], [12509852874546367857, "serde_json", false, 14495910461204486263], [15616241866830646667, "heck", false, 3043464112815131100], [17143850428905299221, "syn", false, 5579730108986302733], [17525013869477438691, "quote", false, 3221887375918024702], [18036439996138669183, "proc_macro2", false, 13516058042257343418]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/anchor-syn-b89f2b8de8004d73/dep-lib-anchor_syn", "checksum": false}}], "rustflags": [], "metadata": 13335907173658770855, "config": 2202906307356721367, "compile_kind": 0}