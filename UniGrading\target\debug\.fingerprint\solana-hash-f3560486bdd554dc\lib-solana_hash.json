{"rustc": 383397013764560953, "features": "[\"borsh\", \"bytemuck\", \"serde\", \"std\"]", "declared_features": "[\"borsh\", \"bytemuck\", \"default\", \"frozen-abi\", \"serde\", \"std\"]", "target": 14600528003313624360, "profile": 6652793396284126538, "path": 4270233807802362529, "deps": [[6203123018298125816, "borsh", false, 13850553873246415677], [6511429716036861196, "bytemuck", false, 9004261617411574230], [9689903380558560274, "serde", false, 8537716410839897940], [14254950316256772154, "solana_atomic_u64", false, 5875492932077611001], [15246557919602675095, "bytemuck_derive", false, 579081428122340601], [15429715045911386410, "solana_sanitize", false, 4200197951219190093], [16257276029081467297, "serde_derive", false, 110293047266484015], [16521523333828454758, "five8", false, 7058500461284421603]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-hash-f3560486bdd554dc/dep-lib-solana_hash", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}