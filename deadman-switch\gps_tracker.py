import requests
import json
from typing import Optional, Dict
from config import config

class GPSTracker:
    def __init__(self):
        self.timeout = config.GPS_TIMEOUT_SECONDS
    
    def get_current_location(self) -> Optional[Dict]:
        """
        Lấy vị trí GPS hiện tại sử dụng nhiều phương pháp:
        1. IP Geolocation (đơn giản nhất)
        2. Browser Geolocation API (nếu có)
        3. WiFi/Network based location
        """
        
        # Phương pháp 1: IP Geolocation (miễn phí, không chính xác lắm)
        location = self._get_location_by_ip()
        if location:
            return location
        
        # Phương pháp 2: Thử các API khác
        location = self._get_location_by_wifi()
        if location:
            return location
            
        return None
    
    def _get_location_by_ip(self) -> Optional[Dict]:
        """Lấy vị trí qua IP geolocation"""
        try:
            # Sử dụng ipapi.co (miễ<PERSON> ph<PERSON>, 1000 requests/day)
            response = requests.get('https://ipapi.co/json/', timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                
                if 'latitude' in data and 'longitude' in data:
                    return {
                        'latitude': float(data['latitude']),
                        'longitude': float(data['longitude']),
                        'accuracy': 10000,  # IP location không chính xác lắm
                        'source': 'ip_geolocation',
                        'city': data.get('city'),
                        'country': data.get('country_name'),
                        'provider': 'ipapi.co'
                    }
        except Exception as e:
            print(f"IP geolocation error: {e}")
        
        # Backup: sử dụng ipinfo.io
        try:
            response = requests.get('https://ipinfo.io/json', timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                
                if 'loc' in data:
                    lat, lon = data['loc'].split(',')
                    return {
                        'latitude': float(lat),
                        'longitude': float(lon),
                        'accuracy': 10000,
                        'source': 'ip_geolocation',
                        'city': data.get('city'),
                        'country': data.get('country'),
                        'provider': 'ipinfo.io'
                    }
        except Exception as e:
            print(f"Backup IP geolocation error: {e}")
        
        return None
    
    def _get_location_by_wifi(self) -> Optional[Dict]:
        """
        Lấy vị trí qua WiFi networks (Google Geolocation API)
        Cần API key nhưng có free tier
        """
        try:
            # Lấy danh sách WiFi networks (Windows)
            import subprocess
            import re
            
            # Chạy netsh để lấy WiFi info
            result = subprocess.run(
                ['netsh', 'wlan', 'show', 'profiles'],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            if result.returncode != 0:
                return None
            
            # Parse WiFi networks
            networks = []
            for line in result.stdout.split('\n'):
                if 'All User Profile' in line:
                    ssid = line.split(':')[1].strip()
                    networks.append({'ssid': ssid})
            
            if not networks:
                return None
            
            # Sử dụng Mozilla Location Service (miễn phí)
            # https://location.services.mozilla.com/
            payload = {
                'wifiAccessPoints': networks[:5]  # Chỉ lấy 5 networks đầu
            }
            
            response = requests.post(
                'https://location.services.mozilla.com/v1/geolocate?key=test',
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'location' in data:
                    return {
                        'latitude': data['location']['lat'],
                        'longitude': data['location']['lng'],
                        'accuracy': data.get('accuracy', 1000),
                        'source': 'wifi_geolocation',
                        'provider': 'mozilla'
                    }
                    
        except Exception as e:
            print(f"WiFi geolocation error: {e}")
        
        return None
    
    def get_location_from_browser(self, latitude: float, longitude: float, 
                                 accuracy: float = None) -> Dict:
        """
        Nhận vị trí từ browser geolocation API
        (sẽ được gọi từ JavaScript frontend)
        """
        return {
            'latitude': latitude,
            'longitude': longitude,
            'accuracy': accuracy or 100,
            'source': 'browser_geolocation',
            'provider': 'browser'
        }
    
    def format_location_for_maps(self, location: Dict) -> str:
        """Format location thành Google Maps URL"""
        if not location:
            return ""
        
        lat = location['latitude']
        lon = location['longitude']
        return f"https://maps.google.com/?q={lat},{lon}"
    
    def calculate_distance(self, loc1: Dict, loc2: Dict) -> float:
        """
        Tính khoảng cách giữa 2 điểm GPS (km)
        Sử dụng Haversine formula
        """
        import math
        
        lat1, lon1 = math.radians(loc1['latitude']), math.radians(loc1['longitude'])
        lat2, lon2 = math.radians(loc2['latitude']), math.radians(loc2['longitude'])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Bán kính Trái Đất (km)
        r = 6371
        
        return c * r
