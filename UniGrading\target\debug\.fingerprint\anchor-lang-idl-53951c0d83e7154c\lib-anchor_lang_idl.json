{"rustc": 383397013764560953, "features": "[\"build\", \"regex\"]", "declared_features": "[\"build\", \"convert\", \"heck\", \"regex\", \"sha2\"]", "target": 13617976458226247918, "profile": 15657897354478470176, "path": 4266196856864589482, "deps": [[9451456094439810778, "regex", false, 9168112024412287341], [9689903380558560274, "serde", false, 8537716410839897940], [13625485746686963219, "anyhow", false, 16408017755803223262], [15367738274754116744, "serde_json", false, 18216465132187117326], [17037804673887881428, "anchor_lang_idl_spec", false, 10485424968559622903]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-idl-53951c0d83e7154c/dep-lib-anchor_lang_idl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}