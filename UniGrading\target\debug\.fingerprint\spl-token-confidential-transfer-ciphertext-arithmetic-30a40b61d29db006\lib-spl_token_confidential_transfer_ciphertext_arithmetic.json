{"rustc": 383397013764560953, "features": "[]", "declared_features": "[]", "target": 12396604673175817722, "profile": 15657897354478470176, "path": 6402888617613429062, "deps": [[3438806654139177877, "solana_zk_sdk", false, 2684287058135583962], [6511429716036861196, "bytemuck", false, 9004261617411574230], [13077212702700853852, "base64", false, 6139311683886876183], [16234497081792451510, "solana_curve25519", false, 12953209395118152137]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-confidential-transfer-ciphertext-arithmetic-30a40b61d29db006/dep-lib-spl_token_confidential_transfer_ciphertext_arithmetic", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}