{"rustc": 383397013764560953, "features": "[\"no-entrypoint\"]", "declared_features": "[\"no-entrypoint\", \"test-sbf\"]", "target": 3313661393964375702, "profile": 6652793396284126538, "path": 4133065963765228066, "deps": [[790673365560624081, "spl_pod", false, 17600426445546719696], [3438806654139177877, "solana_zk_sdk", false, 2684287058135583962], [6511429716036861196, "bytemuck", false, 9004261617411574230], [13155245355270259291, "spl_token_confidential_transfer_proof_extraction", false, 18214294317205124269], [16016078550530309219, "solana_program", false, 3212434464576765496]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-elgamal-registry-5e3fb66fddc24d50/dep-lib-spl_elgamal_registry", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}