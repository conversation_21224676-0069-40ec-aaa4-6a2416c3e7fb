{"rustc": 383397013764560953, "features": "[\"no-entrypoint\"]", "declared_features": "[\"no-entrypoint\", \"test-sbf\"]", "target": 7485417175019194521, "profile": 6652793396284126538, "path": 9895437573035755852, "deps": [[5157631553186200874, "num_traits", false, 17203946164875098880], [6511429716036861196, "bytemuck", false, 9004261617411574230], [8008191657135824715, "thiserror", false, 16276810871065626392], [9529943735784919782, "arrayref", false, 3101610639674147938], [11263754829263059703, "num_derive", false, 10169912134563083283], [16016078550530309219, "solana_program", false, 3212434464576765496], [16712258961403650142, "num_enum", false, 1395736475570976987]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-18af6dc2c4418fc9/dep-lib-spl_token", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}