{"rustc": 383397013764560953, "features": "[]", "declared_features": "[]", "target": 3267741926779996941, "profile": 15657897354478470176, "path": 7462937913824396449, "deps": [[667922753503464778, "spl_program_error_derive", false, 11229084486270294999], [5157631553186200874, "num_traits", false, 17203946164875098880], [8008191657135824715, "thiserror", false, 16276810871065626392], [11263754829263059703, "num_derive", false, 10169912134563083283], [16016078550530309219, "solana_program", false, 3212434464576765496]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-program-error-61f8de6eec375047/dep-lib-spl_program_error", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}