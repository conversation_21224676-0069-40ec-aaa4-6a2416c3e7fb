{"rustc": 383397013764560953, "features": "[\"convert\", \"heck\", \"sha2\"]", "declared_features": "[\"build\", \"convert\", \"heck\", \"regex\", \"sha2\"]", "target": 13617976458226247918, "profile": 2225463790103693989, "path": 4266196856864589482, "deps": [[9689903380558560274, "serde", false, 8537716410839897940], [9857275760291862238, "sha2", false, 15286038725805067178], [13625485746686963219, "anyhow", false, 16408017755803223262], [15367738274754116744, "serde_json", false, 18216465132187117326], [16131248048418321657, "heck", false, 16574158362988723564], [17037804673887881428, "anchor_lang_idl_spec", false, 10485424968559622903]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-idl-1c5b53d386fc7bd3/dep-lib-anchor_lang_idl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}